<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WDA Monitor - WDA_Reg Monitor</title>
    <!-- Include all the same CSS and JS dependencies as visuals.html -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <style>
        .alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            min-width: 300px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .stat-card {
            border-radius: 10px;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        .stat-card.total-parts { background: linear-gradient(45deg, #4158D0, #C850C0); color: white; }
        .stat-card.found-parts { background: linear-gradient(45deg, #11998e, #38ef7d); color: white; }
        .stat-card.not-found-parts { background: linear-gradient(45deg, #FF416C, #FF4B2B); color: white; }
        .stat-card.not-run-parts { background: linear-gradient(45deg, #755BEA, #FF72C0); color: white; }
        .stat-card.expired-parts { background: linear-gradient(45deg, #FF6B6B, #FF8E8E); color: white; }

        .chart-container {
            position: relative;
            min-height: 400px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            padding: 15px;
            margin-bottom: 20px;
        }

        .filter-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .filter-wrapper {
            margin-bottom: 15px;
        }

        .select2-container {
            width: 100% !important;
        }

        .select2-selection--multiple {
            max-height: 80px;
            overflow-y: auto !important;
            overflow-x: hidden;
        }

        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .chart-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 100;
            border-radius: 8px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            min-width: 300px;
            padding: 15px;
            border-radius: 4px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            display: none;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .notification.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .notification.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .data-table-container {
            margin-top: 2rem;
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }

        .data-table {
            min-width: 1200px;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .data-table th {
            background-color: #f8f9fa;
            cursor: pointer;
            padding: 12px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid #dee2e6;
        }

        .data-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .download-btn {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 5px;
            transition: color 0.2s;
        }

        .download-btn:hover {
            color: #0d6efd;
        }

        .navbar {
            margin-bottom: 20px;
        }

        /* Enhanced visual effects */
        .chart-container {
            transition: all 0.3s ease;
            border-radius: 12px;
            overflow: hidden;
        }

        .chart-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stat-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .stat-icon {
            transition: all 0.3s ease;
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }

        /* Loading animations */
        .chart-loading {
            background: linear-gradient(45deg, rgba(255,255,255,0.9), rgba(248,249,250,0.9));
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .chart-container {
                margin-bottom: 2rem;
            }

            .stat-card {
                margin-bottom: 1rem;
            }

            .filter-section {
                padding: 15px;
            }

            .chart-header h4 {
                font-size: 1.1rem;
            }
        }

        /* Custom scrollbar for data table */
        .data-table-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .data-table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .data-table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .data-table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Enhanced filter controls */
        .filter-wrapper {
            position: relative;
        }

        .filter-wrapper label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .select2-container--default .select2-selection--multiple {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .select2-container--default .select2-selection--multiple:focus-within {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        /* Pulse animation for loading states */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .loading-pulse {
            animation: pulse 1.5s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>WDA Monitor
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/visuals">Visualizations</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/wda-reg-monitor">WDA_Reg Monitor</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/import-status">Import Status</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="navbar-text me-3">
                        <i class="fas fa-user me-1"></i>
                        <span id="userFullName">{{ user.full_name if user else 'User' }}</span>
                        <span class="badge bg-light text-dark ms-1" id="userRole">{{ user.role if user else 'Unknown' }}</span>
                    </li>
                    {% if user and user.role == 'admin' %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-cog me-1"></i>Admin
                        </a>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Notification Container -->
    <div id="notification" class="notification"></div>

    <!-- Loading Spinner -->
    <div id="loading" class="loading" style="display: none;">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main Content -->
    <div class="container-fluid mt-3">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-database me-2"></i>WDA_Reg System Monitor
                    <small class="text-muted">- System-wide aggregated data analysis</small>
                </h2>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="row filter-section">
            <div class="col-md-2">
                <button id="refreshData" class="btn btn-outline-primary w-100 mb-3">
                    <i class="fas fa-sync-alt"></i> Refresh Data
                </button>
                <button id="forceRefreshData" class="btn btn-outline-warning w-100 mb-3">
                    <i class="fas fa-database"></i> Force Refresh from DB
                </button>
                <button id="clearFilters" class="btn btn-outline-secondary w-100 mb-3">
                    <i class="fas fa-filter"></i> Clear All Filters
                </button>
                <button id="downloadData" class="btn btn-outline-success w-100 mb-3">
                    <i class="fas fa-download"></i> Download Data
                </button>

                <!-- Data Info Section -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">Data Information</h6>
                    </div>
                    <div class="card-body">
                        <small class="text-muted">
                            <div id="dataTimestamp">Loading...</div>
                            <div id="dataRecordCount">Loading...</div>
                            <div id="dataSource">Loading...</div>
                        </small>
                    </div>
                </div>

                <!-- Filter controls will be added here dynamically -->
                <div id="filterControls"></div>
            </div>

            <!-- Right Column - Visualizations -->
            <div class="col-md-10">
                <!-- Stats Cards -->
                <div class="row mb-4" id="statsCards">
                    <!-- Stats cards will be populated dynamically -->
                </div>

                <!-- Charts Section -->
                <div class="row">
                    <!-- Status Distribution Chart -->
                    <div class="col-md-6 chart-container">
                        <div class="chart-header">
                            <h4>Status Distribution</h4>
                            <button class="download-btn" onclick="downloadChart('statusChart')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="position-relative h-100">
                            <div id="statusChart"></div>
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Priority Distribution Chart -->
                    <div class="col-md-6 chart-container">
                        <div class="chart-header">
                            <h4>Priority Distribution</h4>
                            <button class="download-btn" onclick="downloadChart('priorityChart')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="position-relative h-100">
                            <div id="priorityChart"></div>
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Second Row Charts -->
                <div class="row mt-4">
                    <!-- Manufacturer Distribution Chart -->
                    <div class="col-md-6 chart-container">
                        <div class="chart-header">
                            <h4>Top Manufacturers</h4>
                            <button class="download-btn" onclick="downloadChart('manufacturerChart')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="position-relative h-100">
                            <div id="manufacturerChart"></div>
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- WDA Flag Chart -->
                    <div class="col-md-6 chart-container">
                        <div class="chart-header">
                            <h4>WDA Flag Distribution</h4>
                            <button class="download-btn" onclick="downloadChart('wdaFlagChart')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="position-relative h-100">
                            <div id="wdaFlagChart"></div>
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Third Row Charts -->
                <div class="row mt-4">
                    <!-- CS Distribution Chart -->
                    <div class="col-md-12 chart-container">
                        <div class="chart-header">
                            <h4>CS Labels Distribution (Top 15)</h4>
                            <button class="download-btn" onclick="downloadChart('csChart')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="position-relative h-100">
                            <div id="csChart"></div>
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Timeline Chart -->
                <div class="row mt-4">
                    <div class="col-12 chart-container">
                        <div class="chart-header">
                            <h4>Activity Timeline</h4>
                            <button class="download-btn" onclick="downloadChart('timelineChart')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="position-relative h-100">
                            <div id="timelineChart"></div>
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Table Section -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="chart-container">
                            <div class="chart-header">
                                <h4>Detailed Data Table</h4>
                                <button class="download-btn" onclick="downloadTableData()">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                            <div class="data-table-container">
                                <table class="data-table" id="dataTable">
                                    <thead>
                                        <tr>
                                            <th>Manufacturer</th>
                                            <th>Module</th>
                                            <th>Priority</th>
                                            <th>Status</th>
                                            <th>WDA Flag</th>
                                            <th>CS Labels</th>
                                            <th>LR Date</th>
                                            <th>Count</th>
                                            <th>Expired</th>
                                        </tr>
                                    </thead>
                                    <tbody id="dataTableBody">
                                        <!-- Data will be populated dynamically -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables - only aggregated data, no raw data
        let currentAggregations = {};
        let originalAggregations = {}; // Backup of original data for filter clearing
        let currentFilters = {};
        let filterOptions = {};
        let isFiltered = false;

        // Initialize the page
        $(document).ready(function() {
            loadData();
            setupEventHandlers();
        });

        // Load aggregated data from API (no raw data)
        async function loadData() {
            showLoading(true);
            try {
                const response = await fetch('/api/wda-reg-data');
                const result = await response.json();
                console.log("Data loaded successfully");

                if (result.status === 'success') {
                    currentAggregations = result.aggregations || {};
                    originalAggregations = JSON.parse(JSON.stringify(currentAggregations)); // Deep copy for backup
                    filterOptions = currentAggregations.filter_options || {};
                    isFiltered = false;
                    currentFilters = {};

                    updateUI();

                    // Show different message based on data source
                    const source = result.from_cache ? 'cache' : 'database';
                    showNotification(`Data loaded successfully from ${source} (${result.total_records.toLocaleString()} records)`, 'success');
                } else {
                    throw new Error(result.message || 'Failed to load data');
                }
            } catch (error) {
                console.error('Error loading data:', error);
                showNotification('Error loading data: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // Update the entire UI
        function updateUI() {
            createFilterControls();
            updateStatsCards();
            updateCharts();
            updateDataTable();
            updateDataInfo();
        }

        // Update data information display
        function updateDataInfo() {
            if (currentAggregations.stats) {
                const totalRecords = currentAggregations.stats.total_parts || 0;
                const uniqueManufacturers = currentAggregations.stats.unique_manufacturers || 0;

                // Use current timestamp
                const timestampText = new Date().toLocaleString();

                $('#dataTimestamp').html(`<i class="fas fa-clock"></i> Last Updated: ${timestampText}`);
                $('#dataRecordCount').html(`<i class="fas fa-list"></i> Total Parts: ${totalRecords.toLocaleString()}`);
                $('#dataSource').html(`<i class="fas fa-database"></i> Source: Aggregated Data (Optimized)`);
            } else {
                $('#dataTimestamp').text('No data available');
                $('#dataRecordCount').text('');
                $('#dataSource').text('');
            }
        }

        // Create filter controls dynamically with Select All functionality
        function createFilterControls() {
            const filterContainer = $('#filterControls');
            filterContainer.empty();

            // Use pre-loaded filter options for better performance
            const manNames = filterOptions.man_names || [];
            const moduleNames = filterOptions.module_names || [];
            const priorities = filterOptions.priorities || [];
            const statuses = filterOptions.statuses || [];
            const wdaFlags = filterOptions.wda_flags || [];
            const csLabels = filterOptions.cs_labels || [];

            // Create filter HTML with Select All buttons
            const filtersHTML = `
                <div class="filter-wrapper">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="form-label mb-0">Manufacturer Name</label>
                        <button type="button" class="btn btn-sm btn-outline-primary select-all-btn" data-target="manNameFilter">
                            Select All
                        </button>
                    </div>
                    <select class="form-select select2" id="manNameFilter" multiple>
                        ${manNames.map(name => `<option value="${name}">${name}</option>`).join('')}
                    </select>
                </div>
                <div class="filter-wrapper">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="form-label mb-0">Module Name</label>
                        <button type="button" class="btn btn-sm btn-outline-primary select-all-btn" data-target="moduleNameFilter">
                            Select All
                        </button>
                    </div>
                    <select class="form-select select2" id="moduleNameFilter" multiple>
                        ${moduleNames.map(name => `<option value="${name}">${name}</option>`).join('')}
                    </select>
                </div>
                <div class="filter-wrapper">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="form-label mb-0">Priority</label>
                        <button type="button" class="btn btn-sm btn-outline-primary select-all-btn" data-target="priorityFilter">
                            Select All
                        </button>
                    </div>
                    <select class="form-select select2" id="priorityFilter" multiple>
                        ${priorities.map(p => `<option value="${p}">${p}</option>`).join('')}
                    </select>
                </div>
                <div class="filter-wrapper">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="form-label mb-0">Status</label>
                        <button type="button" class="btn btn-sm btn-outline-primary select-all-btn" data-target="statusFilter">
                            Select All
                        </button>
                    </div>
                    <select class="form-select select2" id="statusFilter" multiple>
                        ${statuses.map(s => `<option value="${s}">${s}</option>`).join('')}
                    </select>
                </div>
                <div class="filter-wrapper">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="form-label mb-0">WDA Flag</label>
                        <button type="button" class="btn btn-sm btn-outline-primary select-all-btn" data-target="wdaFlagFilter">
                            Select All
                        </button>
                    </div>
                    <select class="form-select select2" id="wdaFlagFilter" multiple>
                        ${wdaFlags.map(flag => `<option value="${flag}">${flag === 'Y' ? 'Yes' : 'No'}</option>`).join('')}
                    </select>
                </div>
                <div class="filter-wrapper">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="form-label mb-0">Expired Status</label>
                        <button type="button" class="btn btn-sm btn-outline-primary select-all-btn" data-target="expiredFilter">
                            Select All
                        </button>
                    </div>
                    <select class="form-select select2" id="expiredFilter" multiple>
                        <option value="true">Expired</option>
                        <option value="false">Not Expired</option>
                    </select>
                </div>
                <div class="filter-wrapper">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="form-label mb-0">CS Labels</label>
                        <button type="button" class="btn btn-sm btn-outline-primary select-all-btn" data-target="csFilter">
                            Select All
                        </button>
                    </div>
                    <select class="form-select select2" id="csFilter" multiple>
                        ${csLabels.map(label => `<option value="${label}">${label}</option>`).join('')}
                    </select>
                </div>
            `;

            filterContainer.html(filtersHTML);

            // Initialize Select2
            $('.select2').select2({
                placeholder: 'Select options...',
                allowClear: true,
                width: '100%'
            });

            // Add event listeners for filters
            $('.select2').on('change', applyFilters);

            // Add event listeners for Select All buttons
            $('.select-all-btn').on('click', function() {
                const targetId = $(this).data('target');
                const $select = $('#' + targetId);
                const $button = $(this);

                if ($button.text().trim() === 'Select All') {
                    // Select all options
                    $select.find('option').prop('selected', true);
                    $select.trigger('change');
                    $button.text('Deselect All');
                    $button.removeClass('btn-outline-primary').addClass('btn-outline-secondary');
                } else {
                    // Deselect all options
                    $select.find('option').prop('selected', false);
                    $select.trigger('change');
                    $button.text('Select All');
                    $button.removeClass('btn-outline-secondary').addClass('btn-outline-primary');
                }
            });

            // Update button text based on selection
            $('.select2').on('change', function() {
                const selectId = $(this).attr('id');
                const $button = $(`.select-all-btn[data-target="${selectId}"]`);
                const selectedCount = $(this).val() ? $(this).val().length : 0;
                const totalCount = $(this).find('option').length;

                if (selectedCount === totalCount && totalCount > 0) {
                    $button.text('Deselect All');
                    $button.removeClass('btn-outline-primary').addClass('btn-outline-secondary');
                } else {
                    $button.text('Select All');
                    $button.removeClass('btn-outline-secondary').addClass('btn-outline-primary');
                }
            });
        }

        // Apply filters using server-side aggregation API
        async function applyFilters() {
            const manNameFilter = $('#manNameFilter').val();
            const moduleNameFilter = $('#moduleNameFilter').val();
            const priorityFilter = $('#priorityFilter').val();
            const statusFilter = $('#statusFilter').val();
            const wdaFlagFilter = $('#wdaFlagFilter').val();
            const expiredFilter = $('#expiredFilter').val();
            const csFilter = $('#csFilter').val();

            // Update current filters
            currentFilters = {
                man_names: manNameFilter || [],
                module_names: moduleNameFilter || [],
                priorities: priorityFilter || [],
                statuses: statusFilter || [],
                wda_flags: wdaFlagFilter || [],
                expired_filter: expiredFilter || [],
                cs_labels: csFilter || []
            };

            // Check if any filters are applied
            const hasFilters = Object.values(currentFilters).some(filter => filter.length > 0);

            if (!hasFilters) {
                // No filters applied, restore original aggregations from backup
                currentAggregations = JSON.parse(JSON.stringify(originalAggregations)); // Deep copy from backup
                isFiltered = false;
                updateUI();
                showNotification('Showing all data (no filters applied)', 'success');
                return;
            }

            // Filters applied, get filtered aggregations from server
            try {
                showLoading(true);

                const response = await fetch('/api/wda-reg-filtered-aggregations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(currentFilters)
                });

                const result = await response.json();

                if (result.status === 'success') {
                    currentAggregations = result.aggregations;
                    isFiltered = true;

                    updateStatsCards();
                    updateCharts();
                    updateDataTable();

                    showNotification(`Filters applied (${result.total_records.toLocaleString()} records)`, 'success');
                } else {
                    throw new Error(result.message || 'Failed to apply filters');
                }
            } catch (error) {
                console.error('Error applying filters:', error);
                showNotification('Error applying filters: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // Update statistics cards using current aggregations
        function updateStatsCards() {
            // Always use current aggregations (either original or filtered)
            if (!currentAggregations.stats) {
                console.warn('No stats data available');
                return;
            }

            const stats = currentAggregations.stats;
            const totalParts = stats.total_parts;
            const foundParts = stats.found_parts;
            const notFoundParts = stats.not_found_parts;
            const notRunParts = stats.not_run_parts;
            const expiredParts = stats.expired_parts;
            const uniqueModules = stats.unique_modules;
            const uniqueManufacturers = stats.unique_manufacturers;
            const foundPercentage = stats.found_percentage;
            const notFoundPercentage = stats.not_found_percentage;
            const notRunPercentage = stats.not_run_percentage;
            const expiredPercentage = stats.expired_percentage;

            const statsHTML = `
                <div class="col-md-2">
                    <div class="card stat-card total-parts animate__animated animate__fadeInUp">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Total Parts</h6>
                                    <h2 class="card-title mb-0 counter" data-target="${totalParts}">${totalParts.toLocaleString()}</h2>
                                    <small class="opacity-75">System-wide total</small>
                                </div>
                                <i class="fas fa-boxes stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card stat-card found-parts animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Found Parts</h6>
                                    <h2 class="card-title mb-0 counter" data-target="${foundParts}">${foundParts.toLocaleString()}</h2>
                                    <small class="opacity-75">${foundPercentage}% of total</small>
                                </div>
                                <i class="fas fa-check-circle stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card stat-card not-found-parts animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Not Found</h6>
                                    <h2 class="card-title mb-0 counter" data-target="${notFoundParts}">${notFoundParts.toLocaleString()}</h2>
                                    <small class="opacity-75">${notFoundPercentage}% of total</small>
                                </div>
                                <i class="fas fa-times-circle stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card stat-card not-run-parts animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Not Run</h6>
                                    <h2 class="card-title mb-0 counter" data-target="${notRunParts}">${notRunParts.toLocaleString()}</h2>
                                    <small class="opacity-75">${notRunPercentage}% of total</small>
                                </div>
                                <i class="fas fa-question-circle stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card stat-card expired-parts animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Expired Parts</h6>
                                    <h2 class="card-title mb-0 counter" data-target="${expiredParts}">${expiredParts.toLocaleString()}</h2>
                                    <small class="opacity-75">${expiredPercentage}% of total</small>
                                </div>
                                <i class="fas fa-exclamation-circle stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.5s; background: linear-gradient(45deg, #667eea, #764ba2);">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Unique Manufacturers</h6>
                                    <h2 class="card-title mb-0">${uniqueManufacturers}</h2>
                                    <small class="opacity-75">Active manufacturers</small>
                                </div>
                                <i class="fas fa-industry stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#statsCards').html(statsHTML);

            // Add counter animation
            animateCounters();
        }

        // Animate counter numbers
        function animateCounters() {
            $('.counter').each(function() {
                const $this = $(this);
                const target = parseInt($this.data('target'));
                const duration = 1500; // 1.5 seconds
                const increment = target / (duration / 16); // 60fps
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    $this.text(Math.floor(current).toLocaleString());
                }, 16);
            });
        }

        // Update charts
        function updateCharts() {
            updateStatusChart();
            updatePriorityChart();
            updateManufacturerChart();
            updateWdaFlagChart();
            updateCsChart();
            updateTimelineChart();
        }

        // Update status distribution chart
        function updateStatusChart() {
            if (!currentAggregations.charts || !currentAggregations.charts.status) {
                console.warn('No status chart data available');
                return;
            }

            const statusData = currentAggregations.charts.status;
            const labels = statusData.labels;
            const values = statusData.values;
            const colors = labels.map(label => statusData.colors[label] || '#95a5a6');

            const data = [{
                values: values,
                labels: labels,
                type: 'pie',
                hole: 0.4,
                textinfo: 'label+percent+value',
                textposition: 'auto',
                hovertemplate: '<b>%{label}</b><br>' +
                              'Count: %{value:,}<br>' +
                              'Percentage: %{percent}<br>' +
                              '<extra></extra>',
                marker: {
                    colors: colors,
                    line: {
                        color: '#ffffff',
                        width: 2
                    }
                },
                textfont: {
                    size: 12,
                    color: '#ffffff'
                }
            }];

            const layout = {
                height: 350,
                margin: { t: 30, b: 30, l: 30, r: 30 },
                showlegend: true,
                legend: {
                    orientation: 'v',
                    x: 1.02,
                    y: 0.5
                },
                font: {
                    family: 'Arial, sans-serif',
                    size: 12
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)'
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                displaylogo: false
            };

            Plotly.newPlot('statusChart', data, layout, config);
        }

        // Update priority distribution chart
        function updatePriorityChart() {
            if (!currentAggregations.charts || !currentAggregations.charts.priority) {
                console.warn('No priority chart data available');
                return;
            }

            const priorityData = currentAggregations.charts.priority;
            const sortedPriorities = priorityData.labels;
            const values = priorityData.values;

            // Create gradient colors based on priority
            const colors = sortedPriorities.map((priority, index) => {
                const intensity = (index + 1) / sortedPriorities.length;
                return `rgba(52, 152, 219, ${0.4 + intensity * 0.6})`;
            });

            const data = [{
                x: sortedPriorities,
                y: values,
                type: 'bar',
                text: values.map(v => v.toLocaleString()),
                textposition: 'auto',
                hovertemplate: '<b>Priority: %{x}</b><br>' +
                              'Count: %{y:,}<br>' +
                              '<extra></extra>',
                marker: {
                    color: colors,
                    line: {
                        color: '#2980b9',
                        width: 1
                    }
                }
            }];

            const layout = {
                height: 350,
                margin: { t: 30, b: 80, l: 60, r: 30 },
                xaxis: {
                    title: {
                        text: 'Priority',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickangle: -45,
                    tickfont: { size: 12 }
                },
                yaxis: {
                    title: {
                        text: 'Count',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickformat: ',',
                    tickfont: { size: 12 }
                },
                font: {
                    family: 'Arial, sans-serif'
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                bargap: 0.3
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                displaylogo: false
            };

            Plotly.newPlot('priorityChart', data, layout, config);
        }

        // Update timeline chart
        function updateTimelineChart() {
            if (!currentAggregations.charts || !currentAggregations.charts.timeline) {
                console.warn('No timeline chart data available');
                return;
            }

            const timelineData = currentAggregations.charts.timeline;
            const sortedDates = timelineData.dates;
            const timelineCounts = {};

            // Convert arrays back to objects for compatibility
            timelineData.dates.forEach((date, index) => {
                timelineCounts[date] = timelineData.counts[index];
            });

            if (sortedDates.length === 0) {
                // Show empty state
                const layout = {
                    height: 350,
                    margin: { t: 30, b: 50, l: 60, r: 30 },
                    annotations: [{
                        text: 'No timeline data available',
                        xref: 'paper',
                        yref: 'paper',
                        x: 0.5,
                        y: 0.5,
                        xanchor: 'center',
                        yanchor: 'middle',
                        showarrow: false,
                        font: { size: 16, color: '#7f8c8d' }
                    }],
                    xaxis: { visible: false },
                    yaxis: { visible: false },
                    paper_bgcolor: 'rgba(0,0,0,0)',
                    plot_bgcolor: 'rgba(0,0,0,0)'
                };
                Plotly.newPlot('timelineChart', [], layout, {responsive: true});
                return;
            }

            // Create traces for each status
            const traces = [];
            const statusColors = {
                'found': '#2ecc71',
                'not found': '#e74c3c',
                'not run': '#f39c12'
            };

            // Total count line
            traces.push({
                x: sortedDates,
                y: sortedDates.map(date => timelineCounts[date]),
                type: 'scatter',
                mode: 'lines+markers',
                name: 'Total Count',
                line: {
                    color: '#3498db',
                    width: 3,
                    shape: 'spline'
                },
                marker: {
                    color: '#2980b9',
                    size: 8,
                    line: { color: '#ffffff', width: 2 }
                },
                hovertemplate: '<b>%{x}</b><br>' +
                              'Total Count: %{y:,}<br>' +
                              '<extra></extra>'
            });

            // Note: Status-specific timeline data is not available in current data structure
            // Only showing total count timeline for now

            const layout = {
                height: 350,
                margin: { t: 30, b: 80, l: 60, r: 30 },
                xaxis: {
                    title: {
                        text: 'Date',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickangle: -45,
                    tickfont: { size: 10 },
                    type: 'category'
                },
                yaxis: {
                    title: {
                        text: 'Count',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickformat: ',',
                    tickfont: { size: 12 }
                },
                font: {
                    family: 'Arial, sans-serif'
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                legend: {
                    orientation: 'h',
                    x: 0.5,
                    xanchor: 'center',
                    y: -0.2
                },
                hovermode: 'x unified'
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                displaylogo: false
            };

            Plotly.newPlot('timelineChart', traces, layout, config);
        }

        // Update manufacturer distribution chart
        function updateManufacturerChart() {
            if (!currentAggregations.charts || !currentAggregations.charts.manufacturer) {
                console.warn('No manufacturer chart data available');
                return;
            }

            const manufacturerData = currentAggregations.charts.manufacturer;
            const sortedManufacturers = manufacturerData.labels.map((label, index) => [label, manufacturerData.values[index]]);

            const data = [{
                x: sortedManufacturers.map(([name, count]) => name),
                y: sortedManufacturers.map(([name, count]) => count),
                type: 'bar',
                text: sortedManufacturers.map(([name, count]) => count.toLocaleString()),
                textposition: 'auto',
                hovertemplate: '<b>Manufacturer: %{x}</b><br>' +
                              'Count: %{y:,}<br>' +
                              '<extra></extra>',
                marker: {
                    color: sortedManufacturers.map((_, index) => {
                        const intensity = (sortedManufacturers.length - index) / sortedManufacturers.length;
                        return `rgba(155, 89, 182, ${0.4 + intensity * 0.6})`;
                    }),
                    line: {
                        color: '#8e44ad',
                        width: 1
                    }
                }
            }];

            const layout = {
                height: 350,
                margin: { t: 30, b: 80, l: 60, r: 30 },
                xaxis: {
                    title: {
                        text: 'Manufacturer ID',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickangle: -45,
                    tickfont: { size: 10 }
                },
                yaxis: {
                    title: {
                        text: 'Count',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickformat: ',',
                    tickfont: { size: 12 }
                },
                font: {
                    family: 'Arial, sans-serif'
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                bargap: 0.3
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                displaylogo: false
            };

            Plotly.newPlot('manufacturerChart', data, layout, config);
        }

        // Update WDA flag distribution chart
        function updateWdaFlagChart() {
            if (!currentAggregations.charts || !currentAggregations.charts.wda_flag) {
                console.warn('No WDA flag chart data available');
                return;
            }

            const wdaFlagData = currentAggregations.charts.wda_flag;
            const labels = wdaFlagData.labels.map(label => label === 'Y' ? 'Yes' : 'No');
            const values = wdaFlagData.values;
            const colors = wdaFlagData.labels.map(label => wdaFlagData.colors[label] || '#95a5a6');

            const data = [{
                values: values,
                labels: labels,
                type: 'pie',
                hole: 0.4,
                textinfo: 'label+percent+value',
                textposition: 'auto',
                hovertemplate: '<b>%{label}</b><br>' +
                              'Count: %{value:,}<br>' +
                              'Percentage: %{percent}<br>' +
                              '<extra></extra>',
                marker: {
                    colors: colors,
                    line: {
                        color: '#ffffff',
                        width: 2
                    }
                },
                textfont: {
                    size: 12,
                    color: '#ffffff'
                }
            }];

            const layout = {
                showlegend: true,
                legend: {
                    orientation: 'v',
                    x: 1,
                    y: 0.5,
                    font: {
                        size: 12,
                        color: '#333'
                    }
                },
                margin: { t: 20, b: 20, l: 20, r: 80 },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                font: {
                    family: 'Arial, sans-serif',
                    size: 12,
                    color: '#333'
                }
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                displaylogo: false
            };

            Plotly.newPlot('wdaFlagChart', data, layout, config);
        }

        // Update CS distribution chart
        function updateCsChart() {
            if (!currentAggregations.charts || !currentAggregations.charts.cs) {
                console.warn('No CS chart data available');
                return;
            }

            const csData = currentAggregations.charts.cs;
            const labels = csData.labels;
            const values = csData.values;

            // Create gradient colors for CS labels
            const colors = labels.map((_, index) => {
                const intensity = (labels.length - index) / labels.length;
                return `rgba(52, 152, 219, ${0.4 + intensity * 0.6})`;
            });

            const data = [{
                x: labels,
                y: values,
                type: 'bar',
                text: values.map(v => v.toLocaleString()),
                textposition: 'auto',
                hovertemplate: '<b>CS Label: %{x}</b><br>' +
                              'Count: %{y:,}<br>' +
                              '<extra></extra>',
                marker: {
                    color: colors,
                    line: {
                        color: '#2980b9',
                        width: 1
                    }
                }
            }];

            const layout = {
                height: 350,
                margin: { t: 30, b: 120, l: 60, r: 30 },
                xaxis: {
                    title: {
                        text: 'CS Labels',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickangle: -45,
                    tickfont: { size: 10 }
                },
                yaxis: {
                    title: {
                        text: 'Count',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickformat: ',',
                    tickfont: { size: 12 }
                },
                font: {
                    family: 'Arial, sans-serif'
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                bargap: 0.3
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                displaylogo: false
            };

            Plotly.newPlot('csChart', data, layout, config);
        }

        // Update expired vs active chart
        function updateExpiredChart() {
            if (!currentAggregations.charts || !currentAggregations.charts.expired) {
                console.warn('No expired chart data available');
                return;
            }

            const expiredData = currentAggregations.charts.expired;
            const activeCount = expiredData.values[0]; // Active is first
            const expiredCount = expiredData.values[1]; // Expired is second

            const data = [{
                values: [activeCount, expiredCount],
                labels: ['Active', 'Expired'],
                type: 'pie',
                hole: 0.4,
                textinfo: 'label+percent+value',
                textposition: 'auto',
                hovertemplate: '<b>%{label}</b><br>' +
                              'Count: %{value:,}<br>' +
                              'Percentage: %{percent}<br>' +
                              '<extra></extra>',
                marker: {
                    colors: ['#27ae60', '#e67e22'],
                    line: {
                        color: '#ffffff',
                        width: 2
                    }
                },
                textfont: {
                    size: 12,
                    color: '#ffffff'
                }
            }];

            const layout = {
                height: 350,
                margin: { t: 30, b: 30, l: 30, r: 30 },
                showlegend: true,
                legend: {
                    orientation: 'v',
                    x: 1.02,
                    y: 0.5
                },
                font: {
                    family: 'Arial, sans-serif',
                    size: 12
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)'
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                displaylogo: false
            };

            Plotly.newPlot('expiredChart', data, layout, config);
        }

        // Update data table using aggregated table data
        function updateDataTable() {
            const tableBody = $('#dataTableBody');
            tableBody.empty();

            if (!currentAggregations.table_data || currentAggregations.table_data.length === 0) {
                tableBody.append('<tr><td colspan="9" class="text-center">No data available</td></tr>');
                return;
            }

            const tableData = currentAggregations.table_data;

            // Sort data by COUNT descending
            const sortedData = [...tableData].sort((a, b) => b.COUNT - a.COUNT);

            sortedData.forEach(item => {
                const expiredBadge = item.is_expired ?
                    '<span class="badge bg-warning">Expired</span>' :
                    '<span class="badge bg-success">Active</span>';

                const statusBadge = getStatusBadge(item.STATUS);

                const wdaFlagBadge = item.WDA_FLAG === 'Y' ?
                    '<span class="badge bg-success">Yes</span>' :
                    '<span class="badge bg-danger">No</span>';

                // Format CS labels for display
                const csLabels = item.CS || '';
                const csDisplay = csLabels ?
                    csLabels.split('|').map(label =>
                        `<span class="badge bg-secondary me-1">${label.trim()}</span>`
                    ).join('') :
                    '<span class="text-muted">N/A</span>';

                const row = `
                    <tr>
                        <td>${item.MAN_NAME}</td>
                        <td>${item.MODULE_NAME}</td>
                        <td><span class="badge bg-info">${item.PRTY}</span></td>
                        <td>${statusBadge}</td>
                        <td>${wdaFlagBadge}</td>
                        <td>${csDisplay}</td>
                        <td>${item.LR_DATE || 'N/A'}</td>
                        <td><strong>${item.COUNT.toLocaleString()}</strong></td>
                        <td>${expiredBadge}</td>
                    </tr>
                `;
                tableBody.append(row);
            });
        }

        // Get status badge HTML
        function getStatusBadge(status) {
            switch(status) {
                case 'found':
                    return '<span class="badge bg-success">Found</span>';
                case 'not found':
                    return '<span class="badge bg-danger">Not Found</span>';
                case 'not run':
                    return '<span class="badge bg-warning">Not Run</span>';
                default:
                    return '<span class="badge bg-secondary">' + status + '</span>';
            }
        }

        // Force refresh data from database
        async function forceRefreshData() {
            showLoading(true);
            try {
                const response = await fetch('/api/refresh-wda-reg-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.status === 'success') {
                    showNotification('Data refreshed from database successfully', 'success');
                    // Reload the data after refresh
                    await loadData();
                } else {
                    throw new Error(result.message || 'Failed to refresh data from database');
                }
            } catch (error) {
                console.error('Error force refreshing data:', error);
                showNotification('Error refreshing data from database: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // Setup event handlers
        function setupEventHandlers() {
            $('#refreshData').on('click', loadData);
            $('#forceRefreshData').on('click', forceRefreshData);
            $('#clearFilters').on('click', clearAllFilters);
            $('#downloadData').on('click', downloadTableData);
        }



        // Clear all filters
        async function clearAllFilters() {
            // Temporarily remove event listeners to prevent triggering applyFilters
            $('.select2').off('change', applyFilters);

            // Clear all select2 values without triggering change events
            $('.select2').val(null).trigger('change.select2');

            // Reset current filters and state
            currentFilters = {};
            isFiltered = false;

            // Reset all Select All buttons
            $('.select-all-btn').each(function() {
                $(this).text('Select All');
                $(this).removeClass('btn-outline-secondary').addClass('btn-outline-primary');
            });

            // Reload original data from cache/API
            await loadData();

            // Re-attach event listeners after data is loaded
            $('.select2').on('change', applyFilters);

            showNotification('All filters cleared', 'success');
        }

        // Download table data using optimized API
        async function downloadTableData() {
            try {
                showLoading(true);

                // Get current filter values
                const filters = {
                    man_names: $('#manNameFilter').val() || [],
                    module_names: $('#moduleNameFilter').val() || [],
                    priorities: $('#priorityFilter').val() || [],
                    statuses: $('#statusFilter').val() || [],
                    wda_flags: $('#wdaFlagFilter').val() || [],
                    expired_filter: $('#expiredFilter').val() || [],
                    cs_labels: $('#csFilter').val() || []
                };

                const response = await fetch('/api/download-wda-reg-filtered', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(filters)
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `wda_reg_filtered_data_${new Date().toISOString().split('T')[0]}.csv`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);

                    showNotification('Data downloaded successfully', 'success');
                } else {
                    throw new Error('Failed to download data');
                }
            } catch (error) {
                console.error('Error downloading data:', error);
                showNotification('Error downloading data: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // Convert data to CSV format
        function convertToCSV(data) {
            const headers = ['MAN_ID', 'MOD_ID', 'PRTY', 'CS', 'LRD2', 'V_NOTFOUND_DAT2', 'STATUS', 'LR_DATE', 'COUNT', 'is_expired'];
            const csvRows = [headers.join(',')];

            data.forEach(item => {
                const row = [
                    item.MAN_ID,
                    item.MOD_ID,
                    item.PRTY,
                    item.CS,
                    item.LRD2 || '',
                    item.V_NOTFOUND_DAT2 || '',
                    item.STATUS,
                    item.LR_DATE || '',
                    item.COUNT,
                    item.is_expired
                ];
                csvRows.push(row.join(','));
            });

            return csvRows.join('\n');
        }

        // Download chart as image
        function downloadChart(chartId) {
            Plotly.downloadImage(chartId, {
                format: 'png',
                width: 800,
                height: 600,
                filename: chartId
            });
        }

        // Show/hide loading spinner
        function showLoading(show) {
            if (show) {
                $('#loading').show();
            } else {
                $('#loading').hide();
            }
        }

        // Show notification
        function showNotification(message, type) {
            const notification = $('#notification');
            notification.removeClass('success error').addClass(type);
            notification.text(message);
            notification.show();

            setTimeout(() => {
                notification.hide();
            }, 5000);
        }

        // Logout function
        function logout() {
            fetch('/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    window.location.href = '/login';
                }
            })
            .catch(error => {
                console.error('Error during logout:', error);
                window.location.href = '/login';
            });
        }
    </script>
</body>
</html>
